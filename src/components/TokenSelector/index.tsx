import {
  <PERSON>,
  Modal,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import Token from "@/types/token.ts";
import React from "react";

interface TabPanelProps {
  tokens: Token[];
  onTokenSelect: (token: Token) => void;
}

const TabPanel = React.memo(({ tokens, onTokenSelect }: TabPanelProps) => {
  const handleTokenClick = React.useCallback(
    (token: Token) => {
      onTokenSelect(token);
    },
    [onTokenSelect],
  );

  return (
    <div className="h-96 overflow-y-auto custom-scrollbar">
      {tokens.map((token) => (
        <div
          key={token.id}
          className="flex items-center w-full py-2 px-4 hover:bg-color3 rounded-lg transition-colors cursor-pointer"
          role="button"
          tabIndex={0}
          onClick={() => handleTokenClick(token)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleTokenClick(token);
            }
          }}
        >
          <Image
            alt={`${token.symbol} logo`}
            className="size-7 rounded-full"
            fallbackSrc="/public/images/icon_coin.svg"
            src={token.logouri}
          />
          <div className="flex flex-col items-start ml-4 flex-1">
            <div className="text-base font-medium text-color8">
              {token.symbol}
            </div>
            <div className="text-sm text-color7 mt-0.5">{token.name}</div>
          </div>
          <div className="text-base font-medium text-color8 mr-2">
            {token.balance.formattedAmount}
          </div>
          <Image
            alt="MetaMask"
            className="size-6 mx-2 opacity-70 hover:opacity-100 transition-opacity"
            src="/public/images/logo_metamask.svg"
          />
          <Image
            alt="External link"
            className="size-6 opacity-70 hover:opacity-100 transition-opacity"
            src="/public/images/icon_external_link.svg"
          />
        </div>
      ))}
    </div>
  );
});

TabPanel.displayName = "TabPanel";

export type TokenSelectorProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  tokens: Token[];
  onTokenSelect?: (token: Token) => void;
};

export default function TokenSelector(props: TokenSelectorProps) {
  const handleTokenSelect = React.useCallback(
    (token: Token) => {
      // 选择token后关闭modal
      props.onOpenChange();
      // 如果有回调函数，调用它
      if (props.onTokenSelect) {
        props.onTokenSelect(token);
      }
    },
    [props],
  );

  // 过滤不同类型的tokens
  const popularTokens = React.useMemo(
    () => props.tokens.filter((token) => token.is_popular),
    [props.tokens],
  );

  const depinTokens = React.useMemo(
    () => props.tokens.filter((token) => token.is_depintoken),
    [props.tokens],
  );

  return (
    <Modal
      className="min-w-[32rem]"
      isOpen={props.isOpen}
      onOpenChange={props.onOpenChange}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-xl font-medium text-color8">
              Select Token
            </ModalHeader>
            <ModalBody>
              <Tabs
                classNames={{
                  tabList:
                    "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-purple1",
                  tab: "max-w-fit px-0 h-12",
                  tabContent:
                    "group-data-[selected=true]:text-purple1 text-color7 text-md",
                }}
                size="md"
                variant="underlined"
              >
                <Tab key="popular" title="Popular Tokens">
                  <TabPanel
                    tokens={
                      popularTokens.length > 0 ? popularTokens : props.tokens
                    }
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
                <Tab key="depin" title="Depin Tokens">
                  <TabPanel
                    tokens={depinTokens.length > 0 ? depinTokens : props.tokens}
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
                <Tab key="all" title="All Tokens">
                  <TabPanel
                    tokens={props.tokens}
                    onTokenSelect={handleTokenSelect}
                  />
                </Tab>
              </Tabs>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
