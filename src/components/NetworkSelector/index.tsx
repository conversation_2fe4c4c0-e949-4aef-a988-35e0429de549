import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@heroui/dropdown";
import { Network } from "@/types/network.ts";
import { Image } from "@heroui/react";
import { ReactNode, useCallback } from "react";
import { cn } from "@/lib/utils";

export type NetworkSelectorProps = {
  children?: ReactNode;
  className?: string;
  networks: Network[];
  selectionChainId: number;
  onSelectedNetwork: (network: Network) => void;
};

export function NetworkSelector(props: NetworkSelectorProps) {
  const selectNetwork = useCallback(
    (chainId: string | number) => {
      const numericChainId =
        typeof chainId === "string" ? Number(chainId) : chainId;
      const network = props.networks.find((n) => n.chainId === numericChainId);
      network && props.onSelectedNetwork(network);
    },
    [props.networks, props.onSelectedNetwork],
  );

  return (
    <Dropdown className={cn(props.className)}>
      <DropdownTrigger>{props.children}</DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        aria-label="Network selection"
        selectedKeys={new Set([String(props.selectionChainId)])}
        selectionMode="single"
        variant="flat"
        onSelectionChange={(keys) => {
          keys.currentKey && selectNetwork(keys.currentKey);
        }}
      >
        {props.networks.map((network) => (
          <DropdownItem key={String(network.chainId)}>
            <div className="flex items-center">
              <Image
                alt={network.name}
                className="size-6"
                src={network.logoUrl}
              />
              <div className="text-lg text-color8 ml-2">{network.name}</div>
            </div>
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
