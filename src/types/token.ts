import { BigIntState } from "@/store/standard/BigIntState.ts";
import { makeAutoObservable } from "mobx";
import { networks } from "@/config/network.ts";
import { isValidContractAddress } from "@/lib/utils";

export default class Token {
  id?: number;
  chainid?: number;
  dest_chainid?: number;
  address?: string;
  ctoken_address?: string;
  dest_address?: string;
  name?: string;
  decimals?: number;
  symbol?: string;
  logouri?: string;
  cashier?: string;
  quick_swap_from?: string;
  quick_swap?: string;
  token_only_dest_wrapper?: string;
  need_unwrapper?: boolean;
  is_popular?: boolean;
  is_depintoken?: boolean;
  is_wrapped?: boolean;
  createat?: string;
  updateat?: string;
  router_address?: string;
  usdc_dest_wrapper?: string;
  ctoken?: string;

  balance: BigIntState = new BigIntState({});
  allowanceForCashier: BigIntState = new BigIntState({});

  constructor(args: Partial<BigIntState>) {
    Object.assign(this, args);
    makeAutoObservable(this);
  }

  setBalance(val: bigint) {
    this.balance.setValue(val);
  }

  get isNative() {
    return (
      this.address === "0x0000000000000000000000000000000000000000" ||
      this.address === "00000000000000000000000000000000000000000000"
    );
  }

  /**
   * 获取代币的 spender 地址
   * 根据代币类型和网络配置确定合适的授权地址
   * 使用代币的 dest_chainid 属性作为目标链
   * @returns spender 地址或 null
   * @throws Error 如果代币缺少 dest_chainid 属性或目标网络配置不存在
   */
  getSpenderAddress(): string | null {
    try {
      // 如果是原生代币，不需要授权
      if (this.isNative) {
        return null;
      }

      // 获取当前链的网络配置
      const fromNetwork = networks.find(
        (network) => network.chainId === this.chainid,
      );
      if (!fromNetwork) {
        console.warn(
          `No network configuration found for chainId: ${this.chainid}`,
        );
        return null;
      }

      // 确定目标网络
      if (!this.dest_chainid) {
        throw new Error(`代币 ${this.symbol} 缺少必需的 dest_chainid 属性`);
      }

      // 使用代币的 dest_chainid 属性
      const destNetwork = fromNetwork.destNetworks.find(
        (network) => network.chainId === this.dest_chainid,
      );
      if (!destNetwork) {
        throw new Error(`未找到目标网络配置，chainId: ${this.dest_chainid}`);
      }

      const crossOptions = destNetwork.crossOptions;
      if (!crossOptions) {
        console.warn(
          `No cross options found for destination network: ${destNetwork.name}`,
        );
        return null;
      }

      // 使用与 depositStore 相同的策略选择 spender 地址
      return this.selectSpenderAddress(fromNetwork, crossOptions);
    } catch (error) {
      console.error(
        `Error getting spender address for token ${this.symbol}:`,
        error,
      );
      return null;
    }
  }

  /**
   * 根据策略选择 spender 地址
   * 实现与 depositStore.getSpenderAddress 相同的逻辑
   * @param fromNetwork - 源网络
   * @param crossOptions - 跨链配置
   * @returns spender 地址或 null
   */
  private selectSpenderAddress(
    fromNetwork: any,
    crossOptions: any,
  ): string | null {
    // 定义 spender 选择策略，按优先级排序
    const spenderStrategies = [
      // Strategy 1: IoTeX network with ctoken - use iotxCrossRouter
      {
        condition: () =>
          fromNetwork.chainId === 4689 && // IoTeX chainId
          !!this.ctoken_address &&
          !!crossOptions.iotxCrossRouter,
        address: () => crossOptions.iotxCrossRouter!,
        description: "IoTeX ctoken -> iotxCrossRouter",
      },

      // Strategy 2: Token has specific router address
      {
        condition: () => !!this.router_address,
        address: () => this.router_address!,
        description: "Token specific router",
      },

      // Strategy 3: ctoken with unwrapper and cashierAutoWrapperRouter
      {
        condition: () =>
          !!this.ctoken_address &&
          !!crossOptions.unwrapper &&
          !!crossOptions.cashierAutoWrapperRouter,
        address: () => crossOptions.cashierAutoWrapperRouter!,
        description: "ctoken with unwrapper -> cashierAutoWrapperRouter",
      },

      // Strategy 4: ctoken with cashierRouter (fallback)
      {
        condition: () => !!this.ctoken_address && !!crossOptions.cashierRouter,
        address: () => crossOptions.cashierRouter!,
        description: "ctoken -> cashierRouter",
      },

      // Strategy 5: Default cashier address
      {
        condition: () => !!crossOptions.cashier,
        address: () => crossOptions.cashier!,
        description: "Default cashier",
      },
    ];

    // 查找第一个匹配的策略
    for (const strategy of spenderStrategies) {
      if (strategy.condition()) {
        const spenderAddress = strategy.address();

        // 验证地址
        if (isValidContractAddress(spenderAddress)) {
          console.log(
            `✓ Spender strategy for ${this.symbol}: ${strategy.description} -> ${spenderAddress}`,
          );
          return spenderAddress;
        } else {
          console.warn(
            `⚠ Invalid spender address from ${strategy.description}: ${spenderAddress}`,
          );
        }
      }
    }

    // 如果没有策略有效，记录详细错误信息
    const errorDetails = [
      `无法为代币 ${this.symbol} 确定授权地址`,
      `源网络: ${fromNetwork.name} (${fromNetwork.chainId})`,
      `代币类型: ${this.ctoken_address ? "ctoken" : "regular"}`,
      `可用选项: ${Object.keys(crossOptions)
        .filter((key) => crossOptions[key])
        .join(", ")}`,
    ];

    console.warn(errorDetails.join("\n"));
    return null;
  }
}

export type NativeToken = {
  symbol: string;
  name: string;
  decimal: number;
  logo: string;
};
