import { makeObservable } from "mobx";
import { BigIntState } from "@/store/standard/BigIntState.ts";
import { NativeToken } from "@/types/token.ts";

export type Contract = `0x${string}`;

export abstract class Network {
  name: string = "";
  fullName: string = "";
  chainId: number = 0;
  logoUrl: string = "";
  rpcUrl: string = "";
  explorerName: string = "";
  explorerURL: string = "";
  nativeToken: NativeToken = {
    symbol: "",
    name: "",
    decimal: 0,
    logo: "",
  };
  destNetworks: Network[] = [];
  crossOptions?: CrossOptions;

  constructor(config: Partial<Network>) {
    Object.assign(this, config);
    makeObservable(this);
  }

  abstract isAddressValid(address: string): boolean;
}

export class CrossOptions {
  cashier: Contract = "0x";
  depositFee: BigIntState = new BigIntState({});
  iotxCrossRouter?: string;
  unwrapper?: string;
  cashierRouter?: string;
  cashierAutoWrapperRouter?: string;

  constructor(args: Partial<CrossOptions>) {
    Object.assign(this, args);
    makeObservable(this);
  }
}
