import { EvmNetwork } from "@/lib/EvmNetwork.ts";
import { CrossOptions } from "@/types/network.ts";

const networks = [
  new EvmNetwork({
    name: "IoTeX",
    fullName: "IoTeX Mainnet",
    chainId: 4689,
    logoUrl: "/images/chain/iotex.svg",
    rpcUrl: "https://babel-api.mainnet.iotex.io/",
    explorerURL: "https://iotexscan.io",
    explorerName: "IoTeX Scan",
    nativeToken: {
      symbol: "IOTX",
      name: "IoTeX Network",
      decimal: 18,
      logo: "https://s2.coinmarketcap.com/static/img/coins/64x64/2777.png",
    },
    destNetworks: [
      new EvmNetwork({
        name: "Ethereum",
        fullName: "Ethereum Mainnet",
        chainId: 1,
        logoUrl: "/images/chain/eth.svg",
        rpcUrl: "https://ethereum.publicnode.com",
        explorerURL: "https://etherscan.io",
        explorerName: "EtherScan",
        crossOptions: new CrossOptions({
          cashier: "******************************************",
          iotxCrossRouter: "******************************************",
          unwrapper: "******************************************",
          cashierAutoWrapperRouter:
            "******************************************",
        }),
      }),
      new EvmNetwork({
        name: "BSC",
        fullName: "BNB Smart Chain Mainnet",
        chainId: 56,
        logoUrl: "/images/chain/bsc.svg",
        rpcUrl:
          "https://rpc.ankr.com/bsc/e6f90ba4600e430626402a1cfd774c2df81ffdb92f1b9e9517e54d8eb2d72c6d",
        explorerURL: "https://bscscan.com",
        explorerName: "BscScan",
        crossOptions: new CrossOptions({
          cashier: "******************************************",
          iotxCrossRouter: "******************************************",
          unwrapper: "******************************************",
          cashierRouter: "0xDe7a42CAE046cb10D2d00f276C36bC7265431879",
          cashierAutoWrapperRouter:
            "******************************************",
        }),
      }),
      new EvmNetwork({
        name: "Polygon",
        fullName: "Polygon Mainnet",
        chainId: 137,
        logoUrl: "/images/chain/polygon.svg",
        rpcUrl: "https://polygon-rpc.com",
        explorerURL: "https://polygonscan.com",
        explorerName: "Polygon Explorer",
        crossOptions: new CrossOptions({
          cashier: "0x8114746E4308a4d3Ff2a74B66414fF35657Fa0E2",
          iotxCrossRouter: "******************************************",
          unwrapper: "******************************************",
          cashierRouter: "0xfdf1d22478019f7275A5Bac4a538df8be6B1fd8E",
          cashierAutoWrapperRouter:
            "******************************************",
        }),
      }),
      new EvmNetwork({
        name: "Solana",
        fullName: "Solana",
        chainId: 20000,
        logoUrl: "/images/chain/solana.svg",
        rpcUrl:
          "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
        explorerURL: "https://solscan.io",
        explorerName: "SolanaScan",
        crossOptions: new CrossOptions({
          cashier: "******************************************",
        }),
      }),
    ],
  }),
  new EvmNetwork({
    name: "Ethereum",
    fullName: "Ethereum Mainnet",
    chainId: 1,
    logoUrl: "/images/chain/eth.svg",
    rpcUrl: "https://ethereum.publicnode.com",
    explorerURL: "https://etherscan.io",
    explorerName: "EtherScan",
    nativeToken: {
      symbol: "ETH",
      name: "Ethereum",
      decimal: 18,
      logo: "https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png",
    },
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
        crossOptions: new CrossOptions({
          cashier: "******************************************",
        }),
      }),
      new EvmNetwork({
        name: "Solana",
        fullName: "Solana",
        chainId: 20000,
        logoUrl: "/images/chain/solana.svg",
        rpcUrl:
          "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
        explorerURL: "https://solscan.io",
        explorerName: "SolanaScan",
      }),
    ],
  }),
  new EvmNetwork({
    name: "BSC",
    fullName: "BNB Smart Chain Mainnet",
    chainId: 56,
    logoUrl: "/images/chain/bsc.svg",
    rpcUrl:
      "https://rpc.ankr.com/bsc/e6f90ba4600e430626402a1cfd774c2df81ffdb92f1b9e9517e54d8eb2d72c6d",
    explorerURL: "https://bscscan.com",
    explorerName: "BscScan",
    nativeToken: {
      symbol: "BNB",
      name: "Binance Coin",
      decimal: 18,
      logo: "https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png",
    },
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
        crossOptions: new CrossOptions({
          cashier: "0x78de1E0b76523Ac6E190F89FFC46571346940204",
        }),
      }),
    ],
  }),
  new EvmNetwork({
    name: "Polygon",
    fullName: "Polygon Mainnet",
    chainId: 137,
    logoUrl: "/images/chain/polygon.svg",
    rpcUrl: "https://polygon-rpc.com",
    explorerURL: "https://polygonscan.com",
    explorerName: "Polygon Explorer",
    nativeToken: {
      symbol: "Pol",
      name: "Polygon Coin",
      decimal: 18,
      logo: "https://s2.coinmarketcap.com/static/img/coins/64x64/3890.png",
    },
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
        crossOptions: new CrossOptions({
          cashier: "0x990B503f8C7353f1caB6f9D5bbF8f0Be2718D731",
        }),
      }),
    ],
  }),
  new EvmNetwork({
    name: "Solana",
    fullName: "Solana",
    chainId: 20000,
    logoUrl: "/images/chain/solana.svg",
    rpcUrl:
      "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
    explorerURL: "https://solscan.io",
    explorerName: "SolanaScan",
    nativeToken: {
      symbol: "SOL",
      name: "IoTeX Network",
      decimal: 9,
      logo: "/images/chain/solana.svg",
    },
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
        crossOptions: new CrossOptions({
          cashier: "******************************************",
        }),
      }),
      new EvmNetwork({
        name: "Ethereum",
        fullName: "Ethereum Mainnet",
        chainId: 1,
        logoUrl: "/images/chain/eth.svg",
        rpcUrl: "https://ethereum.publicnode.com",
        explorerURL: "https://etherscan.io",
        explorerName: "EtherScan",
      }),
    ],
  }),
];

export { networks };
