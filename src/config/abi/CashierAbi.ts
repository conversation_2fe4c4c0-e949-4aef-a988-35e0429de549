export const CASHIER_ABI = [
  // depositTo(address,address,uint256,bytes) function
  {
    constant: false,
    inputs: [
      { name: "_token", type: "address", internalType: "address" },
      { name: "_to", type: "address", internalType: "address" },
      { name: "_amount", type: "uint256", internalType: "uint256" },
      { name: "_payload", type: "bytes", internalType: "bytes" },
    ],
    name: "depositTo",
    outputs: [],
    payable: true,
    stateMutability: "payable",
    type: "function",
  },
  // depositFee() function
  {
    constant: true,
    inputs: [],
    name: "depositFee",
    outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
] as const;

export class CashierAbi {
  static getFunction<T extends string>(functionName: T) {
    const func = CASHIER_ABI.find(
      (item) => item.type === "function" && item.name === functionName,
    );
    if (!func) {
      throw new Error(`Function ${functionName} not found in CashierV2 ABI`);
    }
    return [func] as const;
  }

  static readonly Functions = {
    depositTo: CashierAbi.getFunction("depositTo"),
    depositFee: CashierAbi.getFunction("depositFee"),
  } as const;
}
