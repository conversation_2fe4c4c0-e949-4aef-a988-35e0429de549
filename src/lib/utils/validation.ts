/**
 * 验证工具模块
 * 包含各种数据验证和格式检查功能
 */

import { isAddress } from "viem";

/**
 * 验证金额输入是否有效
 * @param value - 要验证的输入值
 * @returns 如果输入有效返回 true，否则返回 false
 *
 * @example
 * validateAmountInput("") // true (允许空字符串)
 * validateAmountInput("123") // true
 * validateAmountInput("123.45") // true
 * validateAmountInput("0.123") // true
 * validateAmountInput("123.45.67") // false (多个小数点)
 * validateAmountInput("01") // false (以多个0开头)
 * validateAmountInput("-123") // false (负数)
 * validateAmountInput("abc") // false (非数字)
 */
export function validateAmountInput(value: string): boolean {
  // 允许空字符串
  if (value === "") {
    return true;
  }

  // 只允许数字和一个小数点
  const regex = /^[0-9]*\.?[0-9]*$/;
  if (!regex.test(value)) {
    return false;
  }

  // 不允许多个小数点
  if ((value.match(/\./g) || []).length > 1) {
    return false;
  }

  // 不允许以多个0开头（除非是0.xxx格式）
  if (value.length > 1 && value[0] === "0" && value[1] !== ".") {
    return false;
  }

  // 转换为数字检查是否为负数
  const numValue = parseFloat(value);
  if (!isNaN(numValue) && numValue < 0) {
    return false;
  }

  return true;
}

/**
 * 验证是否是合法的 EVM 地址
 * @param address - 要验证的地址字符串
 * @returns 如果是合法的 EVM 地址返回 true，否则返回 false
 */
export function validateEvmAddress(
  address: string | null | undefined,
): boolean {
  if (!address) {
    return false;
  }

  try {
    return isAddress(address);
  } catch (error) {
    return false;
  }
}

/**
 * 验证邮箱格式
 * @param email - 邮箱字符串
 * @returns 如果格式正确返回 true
 *
 * @example
 * validateEmail("<EMAIL>") // true
 * validateEmail("invalid-email") // false
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证 URL 格式
 * @param url - URL 字符串
 * @returns 如果格式正确返回 true
 *
 * @example
 * validateUrl("https://example.com") // true
 * validateUrl("invalid-url") // false
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证手机号格式（支持多种格式）
 * @param phone - 手机号字符串
 * @returns 如果格式正确返回 true
 *
 * @example
 * validatePhone("+1234567890") // true
 * validatePhone("************") // true
 * validatePhone("invalid") // false
 */
export function validatePhone(phone: string): boolean {
  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, "");
  // 检查是否为10-15位数字
  return cleaned.length >= 10 && cleaned.length <= 15;
}

/**
 * 验证密码强度
 * @param password - 密码字符串
 * @param options - 验证选项
 * @returns 验证结果对象
 *
 * @example
 * validatePassword("MyPass123!")
 * // { isValid: true, score: 4, issues: [] }
 */
export function validatePassword(
  password: string,
  options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  } = {},
): {
  isValid: boolean;
  score: number; // 0-4
  issues: string[];
} {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true,
  } = options;

  const issues: string[] = [];
  let score = 0;

  // 长度检查
  if (password.length < minLength) {
    issues.push(`密码长度至少需要 ${minLength} 位`);
  } else {
    score++;
  }

  // 大写字母
  if (requireUppercase && !/[A-Z]/.test(password)) {
    issues.push("密码需要包含大写字母");
  } else if (/[A-Z]/.test(password)) {
    score++;
  }

  // 小写字母
  if (requireLowercase && !/[a-z]/.test(password)) {
    issues.push("密码需要包含小写字母");
  } else if (/[a-z]/.test(password)) {
    score++;
  }

  // 数字
  if (requireNumbers && !/\d/.test(password)) {
    issues.push("密码需要包含数字");
  } else if (/\d/.test(password)) {
    score++;
  }

  // 特殊字符
  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    issues.push("密码需要包含特殊字符");
  } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score++;
  }

  return {
    isValid: issues.length === 0,
    score: Math.min(score, 4),
    issues,
  };
}

/**
 * 验证 JSON 格式
 * @param jsonString - JSON 字符串
 * @returns 如果格式正确返回 true
 *
 * @example
 * validateJson('{"key": "value"}') // true
 * validateJson('invalid json') // false
 */
export function validateJson(jsonString: string): boolean {
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证十六进制字符串
 * @param hex - 十六进制字符串
 * @param requirePrefix - 是否需要 0x 前缀
 * @returns 如果格式正确返回 true
 *
 * @example
 * validateHex("0x1234abcd") // true
 * validateHex("1234abcd", false) // true
 * validateHex("xyz") // false
 */
export function validateHex(
  hex: string,
  requirePrefix: boolean = true,
): boolean {
  if (requirePrefix) {
    if (!hex.startsWith("0x")) return false;
    hex = hex.slice(2);
  }

  return /^[0-9a-fA-F]*$/.test(hex);
}

/**
 * 验证日期格式
 * @param dateString - 日期字符串
 * @param format - 期望的格式（可选）
 * @returns 如果格式正确返回 true
 *
 * @example
 * validateDate("2023-12-25") // true
 * validateDate("invalid-date") // false
 */
export function validateDate(dateString: string, _format?: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * 验证数值范围
 * @param value - 要验证的数值
 * @param min - 最小值
 * @param max - 最大值
 * @returns 如果在范围内返回 true
 *
 * @example
 * validateRange(5, 1, 10) // true
 * validateRange(15, 1, 10) // false
 */
export function validateRange(
  value: number,
  min: number,
  max: number,
): boolean {
  return value >= min && value <= max;
}

/**
 * 验证字符串长度
 * @param str - 要验证的字符串
 * @param minLength - 最小长度
 * @param maxLength - 最大长度
 * @returns 如果长度符合要求返回 true
 *
 * @example
 * validateLength("hello", 3, 10) // true
 * validateLength("hi", 3, 10) // false
 */
export function validateLength(
  str: string,
  minLength: number,
  maxLength: number,
): boolean {
  return str.length >= minLength && str.length <= maxLength;
}

/**
 * 验证是否只包含字母数字
 * @param str - 要验证的字符串
 * @returns 如果只包含字母数字返回 true
 *
 * @example
 * validateAlphanumeric("abc123") // true
 * validateAlphanumeric("abc-123") // false
 */
export function validateAlphanumeric(str: string): boolean {
  return /^[a-zA-Z0-9]*$/.test(str);
}

/**
 * 自定义验证器类型
 */
export type Validator<T = any> = (
  value: T,
) => boolean | { isValid: boolean; message?: string };

/**
 * 组合验证器
 * @param validators - 验证器数组
 * @returns 组合后的验证器
 *
 * @example
 * const validator = combineValidators([
 *   (v) => v.length > 0 || { isValid: false, message: "不能为空" },
 *   (v) => v.length < 100 || { isValid: false, message: "太长了" }
 * ]);
 */
export function combineValidators<T>(
  validators: Validator<T>[],
): (value: T) => { isValid: boolean; messages: string[] } {
  return (value: T) => {
    const messages: string[] = [];
    let isValid = true;

    for (const validator of validators) {
      const result = validator(value);

      if (typeof result === "boolean") {
        if (!result) {
          isValid = false;
        }
      } else {
        if (!result.isValid) {
          isValid = false;
          if (result.message) {
            messages.push(result.message);
          }
        }
      }
    }

    return { isValid, messages };
  };
}

/**
 * 验证工具集合
 */
export const validationUtils = {
  // 基础验证
  amount: validateAmountInput,
  evmAddress: validateEvmAddress,
  email: validateEmail,
  url: validateUrl,
  phone: validatePhone,
  password: validatePassword,
  json: validateJson,
  hex: validateHex,
  date: validateDate,

  // 范围和长度
  range: validateRange,
  length: validateLength,
  alphanumeric: validateAlphanumeric,

  // 组合验证
  combine: combineValidators,
} as const;

export default validationUtils;
