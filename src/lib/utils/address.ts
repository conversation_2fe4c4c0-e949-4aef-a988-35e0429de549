/**
 * 地址处理工具模块
 * 包含地址验证、格式化和转换功能
 */

import { isAddress, getAddress } from "viem";

/**
 * 验证是否是合法的 EVM 地址
 * @param address - 要验证的地址字符串
 * @returns 如果是合法的 EVM 地址返回 true，否则返回 false
 *
 * @example
 * isValidEvmAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8") // true
 * isValidEvmAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // true (42 characters)
 * isValidEvmAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df") // false (too short)
 * isValidEvmAddress("742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // false (no 0x prefix)
 * isValidEvmAddress("0xGGGd35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // false (invalid hex)
 * isValidEvmAddress("") // false
 * isValidEvmAddress(null) // false
 */
export function isValidEvmAddress(address: string | null | undefined): boolean {
  if (!address) {
    return false;
  }

  try {
    return isAddress(address);
  } catch (error) {
    return false;
  }
}

/**
 * 验证是否是有效的合约地址（非零地址且格式正确）
 * @param address - 要验证的地址字符串
 * @returns 如果是有效的合约地址返回 true，否则返回 false
 * 
 * @example
 * isValidContractAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // true
 * isValidContractAddress("0x0000000000000000000000000000000000000000") // false (零地址)
 * isValidContractAddress("0x") // false (空地址)
 * isValidContractAddress("") // false (空字符串)
 */
export function isValidContractAddress(address: string): boolean {
  return !!(
    address &&
    address !== "0x" &&
    address !== "0x0000000000000000000000000000000000000000" &&
    address.length === 42 &&
    address.startsWith("0x") &&
    isValidEvmAddress(address)
  );
}

/**
 * 检查是否是零地址
 * @param address - 要检查的地址
 * @returns 如果是零地址返回 true
 * 
 * @example
 * isZeroAddress("0x0000000000000000000000000000000000000000") // true
 * isZeroAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // false
 */
export function isZeroAddress(address: string): boolean {
  return address === "0x0000000000000000000000000000000000000000";
}

/**
 * 获取地址的校验和格式（混合大小写）
 * @param address - 要格式化的地址
 * @returns 校验和格式的地址，如果无效则返回原地址
 * 
 * @example
 * getChecksumAddress("0x742d35cc6634c0532925a3b8d4c9db96c4b4df8a") 
 * // "0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a"
 */
export function getChecksumAddress(address: string): string {
  try {
    return getAddress(address);
  } catch {
    return address;
  }
}

/**
 * 比较两个地址是否相同（忽略大小写）
 * @param address1 - 第一个地址
 * @param address2 - 第二个地址
 * @returns 如果地址相同返回 true
 * 
 * @example
 * isSameAddress("0x742d35cc...", "0x742D35CC...") // true
 * isSameAddress("0x742d35cc...", "0x123456...") // false
 */
export function isSameAddress(
  address1: string | null | undefined,
  address2: string | null | undefined
): boolean {
  if (!address1 || !address2) return false;
  
  try {
    return getAddress(address1) === getAddress(address2);
  } catch {
    return address1.toLowerCase() === address2.toLowerCase();
  }
}

/**
 * 地址省略显示
 * @param address - 地址字符串
 * @param startLength - 保留开头字符数，默认6（包含0x）
 * @param endLength - 保留结尾字符数，默认4
 * @returns 省略后的地址
 * 
 * @example
 * ellipseAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // "0x742d...Df8a"
 */
export function ellipseAddress(
  address: string | null | undefined,
  startLength: number = 6,
  endLength: number = 4,
): string {
  if (!address) return "";
  
  if (address.length <= startLength + endLength + 3) {
    return address;
  }
  
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * 生成地址的标识符（用于显示或比较）
 * @param address - 地址字符串
 * @returns 地址标识符
 * 
 * @example
 * getAddressIdentifier("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // "742d35cc"
 */
export function getAddressIdentifier(address: string): string {
  if (!isValidEvmAddress(address)) return "";
  return address.slice(2, 10).toLowerCase();
}

/**
 * 从地址生成头像颜色（基于地址哈希）
 * @param address - 地址字符串
 * @returns HSL 颜色字符串
 * 
 * @example
 * getAddressColor("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // "hsl(123, 70%, 50%)"
 */
export function getAddressColor(address: string): string {
  if (!isValidEvmAddress(address)) {
    return "hsl(0, 0%, 50%)"; // 默认灰色
  }
  
  // 使用地址的前6个字符生成颜色
  const hash = address.slice(2, 8);
  const hue = parseInt(hash.slice(0, 2), 16) * 360 / 255;
  const saturation = 50 + (parseInt(hash.slice(2, 4), 16) * 30 / 255);
  const lightness = 40 + (parseInt(hash.slice(4, 6), 16) * 20 / 255);
  
  return `hsl(${Math.round(hue)}, ${Math.round(saturation)}%, ${Math.round(lightness)}%)`;
}

/**
 * 验证地址列表中是否包含指定地址
 * @param address - 要查找的地址
 * @param addressList - 地址列表
 * @returns 如果包含返回 true
 * 
 * @example
 * isAddressInList("0x742d35cc...", ["0x742D35CC...", "0x123456..."]) // true
 */
export function isAddressInList(
  address: string,
  addressList: string[]
): boolean {
  if (!isValidEvmAddress(address)) return false;
  
  const checksumAddress = getChecksumAddress(address);
  return addressList.some(addr => isSameAddress(addr, checksumAddress));
}

/**
 * 地址类型检测
 */
export const addressTypes = {
  /**
   * 检查是否是 EOA（外部拥有账户）地址格式
   * 注意：这只是格式检查，真正的 EOA 检测需要链上查询
   */
  isEOAFormat: (address: string): boolean => {
    return isValidEvmAddress(address) && !isZeroAddress(address);
  },

  /**
   * 检查是否可能是合约地址格式
   * 注意：这只是格式检查，真正的合约检测需要链上查询
   */
  isContractFormat: (address: string): boolean => {
    return isValidContractAddress(address);
  },

  /**
   * 检查是否是多签钱包地址格式（基于常见模式）
   */
  isMultisigFormat: (address: string): boolean => {
    // 这里可以添加已知多签钱包的地址模式检测
    // 目前只做基本的有效性检查
    return isValidContractAddress(address);
  },
} as const;

/**
 * 地址工具集合
 */
export const addressUtils = {
  // 验证
  isValid: isValidEvmAddress,
  isValidEvm: isValidEvmAddress,
  isValidContract: isValidContractAddress,
  isZero: isZeroAddress,
  isSame: isSameAddress,
  isInList: isAddressInList,
  
  // 格式化
  getChecksum: getChecksumAddress,
  ellipse: ellipseAddress,
  getIdentifier: getAddressIdentifier,
  getColor: getAddressColor,
  
  // 类型检测
  types: addressTypes,
} as const;

export default addressUtils;
