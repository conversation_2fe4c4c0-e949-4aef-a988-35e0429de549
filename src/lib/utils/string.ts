/**
 * 字符串处理工具模块
 * 包含各种字符串操作和格式化功能
 */

/**
 * 字符串省略处理，保留开头和结尾部分
 * @param text - 要处理的字符串
 * @param startLength - 保留开头的字符数，默认6
 * @param endLength - 保留结尾的字符数，默认4
 * @param separator - 省略符号，默认"..."
 * @returns 处理后的字符串
 *
 * @example
 * ellipseString("0x1234567890abcdef") // "0x1234...cdef"
 * ellipseString("very long text here", 4, 4) // "very...here"
 */
export function ellipseString(
  text: string | null | undefined,
  startLength: number = 6,
  endLength: number = 4,
  separator: string = "...",
): string {
  // 处理空值
  if (!text) {
    return "";
  }

  // 参数验证
  if (startLength < 0 || endLength < 0) {
    console.warn(
      "ellipseString: startLength and endLength must be non-negative",
    );
    return text;
  }

  // 如果字符串长度小于等于要保留的总长度，直接返回原字符串
  const totalKeepLength = startLength + endLength;
  if (text.length <= totalKeepLength + separator.length) {
    return text;
  }

  // 生成省略字符串
  const start = text.slice(0, startLength);
  const end = text.slice(-endLength);

  return `${start}${separator}${end}`;
}

/**
 * 地址省略显示（专门用于以太坊地址）
 * @param address - 地址字符串
 * @param startLength - 保留开头字符数，默认6（包含0x）
 * @param endLength - 保留结尾字符数，默认4
 * @returns 省略后的地址
 *
 * @example
 * ellipseAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // "0x742d...Df8a"
 */
export function ellipseAddress(
  address: string | null | undefined,
  startLength: number = 6,
  endLength: number = 4,
): string {
  return ellipseString(address, startLength, endLength);
}

/**
 * 交易哈希省略显示
 * @param hash - 交易哈希
 * @param startLength - 保留开头字符数，默认8
 * @param endLength - 保留结尾字符数，默认6
 * @returns 省略后的哈希
 *
 * @example
 * ellipseHash("0x1234567890abcdef1234567890abcdef12345678") // "0x123456...345678"
 */
export function ellipseHash(
  hash: string | null | undefined,
  startLength: number = 8,
  endLength: number = 6,
): string {
  return ellipseString(hash, startLength, endLength);
}

/**
 * 首字母大写
 * @param str - 要处理的字符串
 * @returns 首字母大写的字符串
 *
 * @example
 * capitalize("hello world") // "Hello world"
 */
export function capitalize(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 驼峰命名转换为短横线命名
 * @param str - 驼峰命名字符串
 * @returns 短横线命名字符串
 *
 * @example
 * camelToKebab("camelCaseString") // "camel-case-string"
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, "$1-$2").toLowerCase();
}

/**
 * 短横线命名转换为驼峰命名
 * @param str - 短横线命名字符串
 * @returns 驼峰命名字符串
 *
 * @example
 * kebabToCamel("kebab-case-string") // "kebabCaseString"
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * 生成随机字符串
 * @param length - 字符串长度
 * @param charset - 字符集，默认为字母数字
 * @returns 随机字符串
 *
 * @example
 * randomString(8) // "aB3dE7gH"
 * randomString(6, "0123456789") // "123456"
 */
export function randomString(
  length: number,
  charset: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",
): string {
  let result = "";
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}

/**
 * 字符串截断，超出长度时添加省略号
 * @param str - 要截断的字符串
 * @param maxLength - 最大长度
 * @param suffix - 后缀，默认为"..."
 * @returns 截断后的字符串
 *
 * @example
 * truncate("This is a very long string", 10) // "This is a..."
 */
export function truncate(
  str: string,
  maxLength: number,
  suffix: string = "...",
): string {
  if (str.length <= maxLength) {
    return str;
  }
  return str.slice(0, maxLength - suffix.length) + suffix;
}

/**
 * 移除字符串中的所有空白字符
 * @param str - 要处理的字符串
 * @returns 移除空白后的字符串
 *
 * @example
 * removeWhitespace("  hello   world  ") // "helloworld"
 */
export function removeWhitespace(str: string): string {
  return str.replace(/\s/g, "");
}

/**
 * 字符串工具集合
 */
export const stringUtils = {
  ellipse: ellipseString,
  ellipseString,
  ellipseAddress,
  ellipseHash,
  capitalize,
  camelToKebab,
  kebabToCamel,
  randomString,
  truncate,
  removeWhitespace,
} as const;

export default stringUtils;
