/**
 * 数值处理工具模块
 * 包含数值格式化、验证和转换功能
 */

import { formatUnits, parseUnits } from "viem";

/**
 * 格式化余额显示，智能处理小数位数和千分位
 * @param value - 要格式化的数值
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 *
 * @example
 * formatBalance(1234.5678) // "1,234.5678"
 * formatBalance(0.000123) // "0.0001230" (4位有效数字)
 * formatBalance(1234.5678, { decimalPlacesForLarge: 2 }) // "1,234.57"
 * formatBalance(0.000123, { significantDigitsForSmall: 6 }) // "0.000123000"
 */
export function formatBalance(
  value: string | number,
  options: {
    /** 当值小于1时的有效数字位数，默认4位 */
    significantDigitsForSmall?: number;
    /** 当值大于等于1时的小数位数，默认4位 */
    decimalPlacesForLarge?: number;
    /** 是否显示千分位分隔符，默认true */
    useThousandsSeparator?: boolean;
  } = {},
): string {
  const {
    significantDigitsForSmall = 4,
    decimalPlacesForLarge = 4,
    useThousandsSeparator = true,
  } = options;

  if (value === null || value === undefined || value === "") {
    return "0";
  }

  const numValue = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(numValue) || !isFinite(numValue)) {
    return "0";
  }

  if (numValue === 0) {
    return "0";
  }

  const isNegative = numValue < 0;
  const absValue = Math.abs(numValue);

  let formattedValue: string;

  if (absValue < 1) {
    if (absValue < 1e-6) {
      const exponent = Math.floor(Math.log10(absValue));
      const mantissa = absValue / Math.pow(10, exponent);
      const roundedMantissa = parseFloat(
        mantissa.toPrecision(significantDigitsForSmall),
      );
      const result = roundedMantissa * Math.pow(10, exponent);
      formattedValue = result.toFixed(
        -exponent + significantDigitsForSmall - 1,
      );
    } else {
      formattedValue = absValue.toPrecision(significantDigitsForSmall);
      const num = parseFloat(formattedValue);
      formattedValue = num.toString();
    }
  } else {
    formattedValue = absValue.toFixed(decimalPlacesForLarge);
  }

  formattedValue = formattedValue.replace(/\.?0+$/, "");

  if (useThousandsSeparator) {
    const parts = formattedValue.split(".");
    const integerPart = parts[0];
    const decimalPart = parts[1];

    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    formattedValue = decimalPart
      ? `${formattedInteger}.${decimalPart}`
      : formattedInteger;
  }

  return isNegative ? `-${formattedValue}` : formattedValue;
}

/**
 * 验证金额输入是否有效
 * @param value - 要验证的输入值
 * @returns 如果输入有效返回 true，否则返回 false
 *
 * @example
 * validateAmountInput("") // true (允许空字符串)
 * validateAmountInput("123") // true
 * validateAmountInput("123.45") // true
 * validateAmountInput("0.123") // true
 * validateAmountInput("123.45.67") // false (多个小数点)
 * validateAmountInput("01") // false (以多个0开头)
 * validateAmountInput("-123") // false (负数)
 * validateAmountInput("abc") // false (非数字)
 */
export function validateAmountInput(value: string): boolean {
  // 允许空字符串
  if (value === "") {
    return true;
  }

  // 只允许数字和一个小数点
  const regex = /^[0-9]*\.?[0-9]*$/;
  if (!regex.test(value)) {
    return false;
  }

  // 不允许多个小数点
  if ((value.match(/\./g) || []).length > 1) {
    return false;
  }

  // 不允许以多个0开头（除非是0.xxx格式）
  if (value.length > 1 && value[0] === "0" && value[1] !== ".") {
    return false;
  }

  // 转换为数字检查是否为负数
  const numValue = parseFloat(value);
  if (!isNaN(numValue) && numValue < 0) {
    return false;
  }

  return true;
}

/**
 * 格式化百分比显示
 * @param value - 数值（0-1之间表示百分比）
 * @param decimals - 小数位数，默认2位
 * @returns 格式化的百分比字符串
 *
 * @example
 * formatPercentage(0.1234) // "12.34%"
 * formatPercentage(0.1234, 1) // "12.3%"
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  if (isNaN(value) || !isFinite(value)) {
    return "0%";
  }
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * 格式化大数值显示（K, M, B, T）
 * @param value - 要格式化的数值
 * @param decimals - 小数位数，默认1位
 * @returns 格式化的字符串
 *
 * @example
 * formatLargeNumber(1234) // "1.2K"
 * formatLargeNumber(1234567) // "1.2M"
 * formatLargeNumber(1234567890) // "1.2B"
 */
export function formatLargeNumber(value: number, decimals: number = 1): string {
  if (isNaN(value) || !isFinite(value)) {
    return "0";
  }

  const absValue = Math.abs(value);
  const sign = value < 0 ? "-" : "";

  if (absValue < 1000) {
    return `${sign}${absValue}`;
  }

  const units = ["", "K", "M", "B", "T"];
  const unitIndex = Math.floor(Math.log10(absValue) / 3);
  const scaledValue = absValue / Math.pow(1000, unitIndex);

  return `${sign}${scaledValue.toFixed(decimals)}${units[unitIndex]}`;
}

/**
 * 安全的数值转换
 * @param value - 要转换的值
 * @param defaultValue - 默认值，默认为0
 * @returns 转换后的数值
 *
 * @example
 * safeNumber("123") // 123
 * safeNumber("abc") // 0
 * safeNumber("abc", 100) // 100
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  const num = Number(value);
  return isNaN(num) || !isFinite(num) ? defaultValue : num;
}

/**
 * 限制数值在指定范围内
 * @param value - 要限制的数值
 * @param min - 最小值
 * @param max - 最大值
 * @returns 限制后的数值
 *
 * @example
 * clamp(5, 0, 10) // 5
 * clamp(-5, 0, 10) // 0
 * clamp(15, 0, 10) // 10
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * 生成指定范围内的随机数
 * @param min - 最小值
 * @param max - 最大值
 * @param decimals - 小数位数，默认0（整数）
 * @returns 随机数
 *
 * @example
 * randomNumber(1, 10) // 5 (整数)
 * randomNumber(1, 10, 2) // 5.67 (2位小数)
 */
export function randomNumber(
  min: number,
  max: number,
  decimals: number = 0,
): number {
  const random = Math.random() * (max - min) + min;
  return decimals === 0 ? Math.floor(random) : Number(random.toFixed(decimals));
}

/**
 * 计算两个数值的百分比差异
 * @param oldValue - 旧值
 * @param newValue - 新值
 * @returns 百分比差异（-1到1之间）
 *
 * @example
 * percentageChange(100, 120) // 0.2 (增长20%)
 * percentageChange(120, 100) // -0.167 (下降16.7%)
 */
export function percentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return newValue === 0 ? 0 : 1;
  return (newValue - oldValue) / oldValue;
}

/**
 * BigInt 相关工具
 */
export const bigIntUtils = {
  /**
   * 安全地将字符串转换为 BigInt
   * @param value - 要转换的值
   * @param decimals - 小数位数
   * @returns BigInt 值
   */
  safeParse: (value: string, decimals: number): bigint => {
    try {
      return parseUnits(value || "0", decimals);
    } catch {
      return 0n;
    }
  },

  /**
   * 安全地将 BigInt 转换为字符串
   * @param value - BigInt 值
   * @param decimals - 小数位数
   * @returns 字符串值
   */
  safeFormat: (value: bigint, decimals: number): string => {
    try {
      return formatUnits(value, decimals);
    } catch {
      return "0";
    }
  },
};

/**
 * 数值工具集合
 */
export const numberUtils = {
  formatBalance,
  validateAmountInput,
  formatPercentage,
  formatLargeNumber,
  safeNumber,
  clamp,
  randomNumber,
  percentageChange,
  bigInt: bigIntUtils,
} as const;

export default numberUtils;
