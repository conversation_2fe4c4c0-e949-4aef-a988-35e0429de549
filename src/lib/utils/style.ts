/**
 * 样式处理工具模块
 * 包含 CSS 类名处理、主题相关和样式计算功能
 */

import clsx, { type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * 合并 Tailwind CSS 类名，处理冲突和条件类名
 * @param inputs - 类名输入
 * @returns 合并后的类名字符串
 *
 * @example
 * cn("px-2 py-1", "px-4") // "py-1 px-4" (px-4 覆盖 px-2)
 * cn("text-red-500", condition && "text-blue-500") // 条件类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 条件类名助手
 * @param condition - 条件
 * @param trueClass - 条件为真时的类名
 * @param falseClass - 条件为假时的类名
 * @returns 类名字符串
 *
 * @example
 * conditionalClass(isActive, "bg-blue-500", "bg-gray-500")
 */
export function conditionalClass(
  condition: boolean,
  trueClass: string,
  falseClass: string = "",
): string {
  return condition ? trueClass : falseClass;
}

/**
 * 变体类名生成器
 * @param base - 基础类名
 * @param variants - 变体映射
 * @param selectedVariants - 选中的变体
 * @returns 合并后的类名
 *
 * @example
 * const buttonClass = variantClass(
 *   "px-4 py-2 rounded",
 *   {
 *     size: { sm: "text-sm", lg: "text-lg" },
 *     color: { primary: "bg-blue-500", secondary: "bg-gray-500" }
 *   },
 *   { size: "sm", color: "primary" }
 * ); // "px-4 py-2 rounded text-sm bg-blue-500"
 */
export function variantClass<T extends Record<string, Record<string, string>>>(
  base: string,
  variants: T,
  selectedVariants: Partial<{ [K in keyof T]: keyof T[K] }>,
): string {
  const variantClasses = Object.entries(selectedVariants)
    .map(([key, value]) => {
      const variantGroup = variants[key];
      return variantGroup && value ? variantGroup[value as string] : "";
    })
    .filter(Boolean);

  return cn(base, ...variantClasses);
}

/**
 * 响应式类名生成器
 * @param classes - 响应式类名映射
 * @returns 合并后的类名
 *
 * @example
 * responsiveClass({
 *   base: "text-center",
 *   sm: "text-left",
 *   md: "text-right",
 *   lg: "text-center"
 * }); // "text-center sm:text-left md:text-right lg:text-center"
 */
export function responsiveClass(classes: {
  base?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  "2xl"?: string;
}): string {
  const { base, sm, md, lg, xl, "2xl": xl2 } = classes;

  return cn(
    base,
    sm && `sm:${sm}`,
    md && `md:${md}`,
    lg && `lg:${lg}`,
    xl && `xl:${xl}`,
    xl2 && `2xl:${xl2}`,
  );
}

/**
 * 状态类名生成器
 * @param baseClass - 基础类名
 * @param states - 状态映射
 * @returns 合并后的类名
 *
 * @example
 * stateClass("button", {
 *   hover: "hover:bg-blue-600",
 *   focus: "focus:ring-2",
 *   disabled: "disabled:opacity-50"
 * }); // "button hover:bg-blue-600 focus:ring-2 disabled:opacity-50"
 */
export function stateClass(
  baseClass: string,
  states: {
    hover?: string;
    focus?: string;
    active?: string;
    disabled?: string;
    visited?: string;
  },
): string {
  const { hover, focus, active, disabled, visited } = states;

  return cn(
    baseClass,
    hover && `hover:${hover}`,
    focus && `focus:${focus}`,
    active && `active:${active}`,
    disabled && `disabled:${disabled}`,
    visited && `visited:${visited}`,
  );
}

/**
 * 主题类名生成器
 * @param lightClass - 浅色主题类名
 * @param darkClass - 深色主题类名
 * @returns 合并后的类名
 *
 * @example
 * themeClass("bg-white text-black", "bg-black text-white")
 * // "bg-white text-black dark:bg-black dark:text-white"
 */
export function themeClass(lightClass: string, darkClass: string): string {
  return cn(lightClass, `dark:${darkClass}`);
}

/**
 * 动画类名生成器
 * @param animation - 动画类型
 * @param duration - 持续时间
 * @param delay - 延迟时间
 * @param easing - 缓动函数
 * @returns 动画类名
 *
 * @example
 * animationClass("fade-in", "300ms", "100ms", "ease-out")
 * // "animate-fade-in duration-300 delay-100 ease-out"
 */
export function animationClass(
  animation: string,
  duration?: string,
  delay?: string,
  easing?: string,
): string {
  const classes = [`animate-${animation}`];

  if (duration) classes.push(`duration-${duration.replace("ms", "")}`);
  if (delay) classes.push(`delay-${delay.replace("ms", "")}`);
  if (easing) classes.push(`ease-${easing}`);

  return cn(...classes);
}

/**
 * 网格类名生成器
 * @param cols - 列数配置
 * @param gap - 间距
 * @param responsive - 是否响应式
 * @returns 网格类名
 *
 * @example
 * gridClass({ base: 1, md: 2, lg: 3 }, "4", true)
 * // "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
 */
export function gridClass(
  cols:
    | number
    | { base?: number; sm?: number; md?: number; lg?: number; xl?: number },
  gap?: string,
  responsive: boolean = true,
): string {
  const baseClasses = ["grid"];

  if (typeof cols === "number") {
    baseClasses.push(`grid-cols-${cols}`);
  } else {
    const { base, sm, md, lg, xl } = cols;
    if (base) baseClasses.push(`grid-cols-${base}`);
    if (responsive) {
      if (sm) baseClasses.push(`sm:grid-cols-${sm}`);
      if (md) baseClasses.push(`md:grid-cols-${md}`);
      if (lg) baseClasses.push(`lg:grid-cols-${lg}`);
      if (xl) baseClasses.push(`xl:grid-cols-${xl}`);
    }
  }

  if (gap) baseClasses.push(`gap-${gap}`);

  return cn(...baseClasses);
}

/**
 * Flexbox 类名生成器
 * @param direction - 方向
 * @param justify - 主轴对齐
 * @param align - 交叉轴对齐
 * @param wrap - 是否换行
 * @param gap - 间距
 * @returns Flexbox 类名
 *
 * @example
 * flexClass("row", "center", "center", false, "4")
 * // "flex flex-row justify-center items-center gap-4"
 */
export function flexClass(
  direction?: "row" | "col" | "row-reverse" | "col-reverse",
  justify?: "start" | "end" | "center" | "between" | "around" | "evenly",
  align?: "start" | "end" | "center" | "baseline" | "stretch",
  wrap?: boolean,
  gap?: string,
): string {
  const classes = ["flex"];

  if (direction) classes.push(`flex-${direction}`);
  if (justify) classes.push(`justify-${justify}`);
  if (align) classes.push(`items-${align}`);
  if (wrap) classes.push("flex-wrap");
  if (gap) classes.push(`gap-${gap}`);

  return cn(...classes);
}

/**
 * 尺寸类名生成器
 * @param width - 宽度
 * @param height - 高度
 * @param maxWidth - 最大宽度
 * @param maxHeight - 最大高度
 * @returns 尺寸类名
 *
 * @example
 * sizeClass("full", "64", "md", "screen")
 * // "w-full h-64 max-w-md max-h-screen"
 */
export function sizeClass(
  width?: string,
  height?: string,
  maxWidth?: string,
  maxHeight?: string,
): string {
  const classes: string[] = [];

  if (width) classes.push(`w-${width}`);
  if (height) classes.push(`h-${height}`);
  if (maxWidth) classes.push(`max-w-${maxWidth}`);
  if (maxHeight) classes.push(`max-h-${maxHeight}`);

  return cn(...classes);
}

/**
 * 样式工具集合
 */
export const styleUtils = {
  // 基础
  cn,
  classNames: cn,

  // 条件和变体
  conditional: conditionalClass,
  variant: variantClass,
  responsive: responsiveClass,
  state: stateClass,
  theme: themeClass,

  // 布局
  grid: gridClass,
  flex: flexClass,
  size: sizeClass,

  // 动画
  animation: animationClass,
} as const;

export default styleUtils;
