/**
 * 工具函数统一导出文件
 * 按功能分类导出，方便使用和维护
 */

// 从各个分类文件导入函数
export {
  // 字符串处理
  ellipseString,
  ellipseAddress,
  ellipseHash,
  capitalize,
  camelToKebab,
  kebabToCamel,
  randomString,
  truncate,
  removeWhitespace,
} from "./string";

export {
  // 样式处理
  cn,
  conditionalClass,
  variantClass,
  responsiveClass,
  stateClass,
  themeClass,
  animationClass,
  gridClass,
  flexClass,
  sizeClass,
} from "./style";

export {
  // 数值格式化和验证
  formatBalance,
  validateAmountInput,
  formatPercentage,
  formatLargeNumber,
  safeNumber,
  clamp,
  randomNumber,
  percentageChange,
} from "./number";

export {
  // 地址验证和处理
  isValidEvmAddress,
  isValidContractAddress,
  isZeroAddress,
  getChecksumAddress,
  isSameAddress,
  getAddressIdentifier,
  getAddressColor,
  isAddressInList,
} from "./address";

export {
  // 验证工具
  validateEmail,
  validateUrl,
  validatePhone,
  validatePassword,
  validateJson,
  validateHex,
  validateDate,
  validateRange,
  validateLength,
  validateAlphanumeric,
  combineValidators,
} from "./validation";

// ============================================================================
// 分类导出 - 按功能组织
// ============================================================================

// 字符串处理工具
export * as StringUtils from "./string";

// 数值处理工具
export * as NumberUtils from "./number";

// 地址处理工具
export * as AddressUtils from "./address";

// 样式处理工具
export * as StyleUtils from "./style";

// 验证工具
export * as ValidationUtils from "./validation";

// ============================================================================
// 便捷的分组导出
// ============================================================================

import {
  ellipseString,
  ellipseAddress,
  ellipseHash,
  capitalize,
  camelToKebab,
  kebabToCamel,
  randomString,
  truncate,
  removeWhitespace,
} from "./string";
import {
  formatBalance,
  validateAmountInput,
  formatPercentage,
  formatLargeNumber,
  safeNumber,
  clamp,
  randomNumber,
  percentageChange,
} from "./number";
import {
  isValidEvmAddress,
  isValidContractAddress,
  isZeroAddress,
  getChecksumAddress,
  isSameAddress,
  getAddressIdentifier,
  getAddressColor,
  isAddressInList,
} from "./address";
import {
  cn,
  conditionalClass,
  variantClass,
  responsiveClass,
  stateClass,
  themeClass,
  animationClass,
  gridClass,
  flexClass,
  sizeClass,
} from "./style";
import {
  validateEmail,
  validateUrl,
  validatePhone,
  validatePassword,
  validateJson,
  validateHex,
  validateDate,
  validateRange,
  validateLength,
  validateAlphanumeric,
  combineValidators,
} from "./validation";

/**
 * 字符串处理工具集合
 */
export const stringUtils = {
  ellipse: ellipseString,
  ellipseString,
  ellipseAddress,
  ellipseHash,
  capitalize,
  camelToKebab,
  kebabToCamel,
  randomString,
  truncate,
  removeWhitespace,
} as const;

/**
 * 数值处理工具集合
 */
export const numberUtils = {
  formatBalance,
  validateAmount: validateAmountInput,
  validateAmountInput,
  formatPercentage,
  formatLargeNumber,
  safeNumber,
  clamp,
  randomNumber,
  percentageChange,
} as const;

/**
 * 地址验证工具集合
 */
export const addressUtils = {
  isValidEvm: isValidEvmAddress,
  isValidContract: isValidContractAddress,
  isValidEvmAddress,
  isValidContractAddress,
  isZero: isZeroAddress,
  getChecksum: getChecksumAddress,
  isSame: isSameAddress,
  ellipse: ellipseAddress,
  getIdentifier: getAddressIdentifier,
  getColor: getAddressColor,
  isInList: isAddressInList,
} as const;

/**
 * 样式处理工具集合
 */
export const styleUtils = {
  cn,
  classNames: cn,
  conditional: conditionalClass,
  variant: variantClass,
  responsive: responsiveClass,
  state: stateClass,
  theme: themeClass,
  animation: animationClass,
  grid: gridClass,
  flex: flexClass,
  size: sizeClass,
} as const;

/**
 * 验证工具集合
 */
export const validationUtils = {
  amount: validateAmountInput,
  evmAddress: isValidEvmAddress,
  contractAddress: isValidContractAddress,
  email: validateEmail,
  url: validateUrl,
  phone: validatePhone,
  password: validatePassword,
  json: validateJson,
  hex: validateHex,
  date: validateDate,
  range: validateRange,
  length: validateLength,
  alphanumeric: validateAlphanumeric,
  combine: combineValidators,
} as const;

// ============================================================================
// 默认导出 - 包含所有工具的对象
// ============================================================================

/**
 * 所有工具函数的集合对象
 * 可以通过 utils.string.ellipse() 或 utils.number.formatBalance() 的方式调用
 */
export default {
  string: stringUtils,
  number: numberUtils,
  address: addressUtils,
  style: styleUtils,
  validation: validationUtils,

  // 直接访问的快捷方式 - 最常用的工具
  ellipseString,
  ellipseAddress,
  formatBalance,
  validateAmountInput,
  isValidEvmAddress,
  isValidContractAddress,
  cn,

  // 其他常用工具
  capitalize,
  formatPercentage,
  formatLargeNumber,
  getChecksumAddress,
  isSameAddress,
} as const;
