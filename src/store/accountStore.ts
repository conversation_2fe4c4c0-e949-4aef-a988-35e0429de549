import { makeAutoObservable } from "mobx";
import { getAccount, watchAccount } from "@wagmi/core";
import { wagmiAdapter } from "@/provider";
import { ellipseString } from "@/lib/utils";

/**
 * 账户状态接口
 */
export interface WalletInfo {
  isConnected: boolean;
  address: string | undefined;
  chainId: number | undefined;
  connector: any;
}

/**
 * 详细的钱包状态接口
 */
export interface WalletStatus extends WalletInfo {
  addressShort: string | undefined;
  connectorName: string | undefined;
}

/**
 * AccountStore - 专门管理账户和钱包相关状态
 */
export class AccountStore {
  // 内部可观察状态
  private _walletInfo: WalletInfo = {
    isConnected: false,
    address: undefined,
    chainId: undefined,
    connector: undefined,
  };

  private unwatchAccount?: () => void;

  constructor() {
    makeAutoObservable(this);
    this.initializeWatcher();
  }

  /**
   * 初始化账户状态监听器
   */
  private initializeWatcher() {
    // 初始化当前状态
    this.updateWalletInfo();

    // 监听账户变化
    this.unwatchAccount = watchAccount(wagmiAdapter.wagmiConfig, {
      onChange: (account) => {
        console.log("Account changed:", account);
        this._walletInfo = {
          isConnected: account.isConnected,
          address: account.address,
          chainId: account.chainId,
          connector: account.connector,
        };
      },
    });
  }

  /**
   * 更新钱包信息
   */
  private updateWalletInfo() {
    const account = getAccount(wagmiAdapter.wagmiConfig);
    this._walletInfo = {
      isConnected: account.isConnected,
      address: account.address,
      chainId: account.chainId,
      connector: account.connector,
    };
  }

  /**
   * 获取当前钱包连接状态和地址
   */
  get walletInfo(): WalletInfo {
    return this._walletInfo;
  }

  /**
   * 检查钱包是否已连接
   */
  get isWalletConnected(): boolean {
    return this.walletInfo.isConnected;
  }

  /**
   * 获取钱包地址
   */
  get walletAddress(): string | undefined {
    return this.walletInfo.address;
  }

  /**
   * 获取当前链ID
   */
  get currentChainId(): number | undefined {
    return this.walletInfo.chainId;
  }

  /**
   * 获取当前连接器
   */
  get currentConnector(): any {
    return this.walletInfo.connector;
  }

  /**
   * 获取钱包连接状态的详细信息
   */
  getWalletStatus(): WalletStatus {
    const info = this.walletInfo;
    return {
      ...info,
      addressShort: info.address
        ? ellipseString(info.address, 6, 4)
        : undefined,
      connectorName: info.connector?.name,
    };
  }

  /**
   * 检查钱包是否连接，如果未连接则抛出错误
   */
  ensureWalletConnected(): string {
    if (!this.isWalletConnected || !this.walletAddress) {
      throw new Error("钱包未连接，请先连接钱包");
    }
    return this.walletAddress;
  }

  /**
   * 检查是否在指定链上
   */
  isOnChain(chainId: number): boolean {
    return this.currentChainId === chainId;
  }

  /**
   * 检查是否在支持的链上
   */
  isOnSupportedChain(supportedChainIds: number[]): boolean {
    return this.currentChainId
      ? supportedChainIds.includes(this.currentChainId)
      : false;
  }

  /**
   * 获取地址的简短显示格式
   */
  getShortAddress(address?: string): string | undefined {
    const targetAddress = address || this.walletAddress;
    if (!targetAddress) return undefined;
    return ellipseString(targetAddress, 6, 4);
  }

  /**
   * 检查地址是否为当前连接的钱包地址
   */
  isCurrentWalletAddress(address: string): boolean {
    return this.walletAddress?.toLowerCase() === address.toLowerCase();
  }

  /**
   * 获取钱包连接状态的摘要信息
   */
  getConnectionSummary(): {
    connected: boolean;
    address?: string;
    shortAddress?: string;
    chainId?: number;
    connectorName?: string;
  } {
    const status = this.getWalletStatus();
    return {
      connected: status.isConnected,
      address: status.address,
      shortAddress: status.addressShort,
      chainId: status.chainId,
      connectorName: status.connectorName,
    };
  }

  /**
   * 等待钱包连接（用于异步操作）
   */
  async waitForConnection(timeout = 10000): Promise<string> {
    return new Promise((resolve, reject) => {
      if (this.isWalletConnected && this.walletAddress) {
        resolve(this.walletAddress);
        return;
      }

      const timer = setTimeout(() => {
        reject(new Error("等待钱包连接超时"));
      }, timeout);

      // 这里可以添加监听钱包连接状态变化的逻辑
      // 简化版本：定期检查
      const checkInterval = setInterval(() => {
        if (this.isWalletConnected && this.walletAddress) {
          clearTimeout(timer);
          clearInterval(checkInterval);
          resolve(this.walletAddress);
        }
      }, 100);
    });
  }

  /**
   * 验证地址格式
   */
  isValidAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * 比较两个地址是否相同（忽略大小写）
   */
  isSameAddress(address1: string, address2: string): boolean {
    return address1.toLowerCase() === address2.toLowerCase();
  }

  /**
   * 获取钱包类型描述
   */
  getWalletTypeDescription(): string {
    const connector = this.currentConnector;
    if (!connector) return "未连接";

    switch (connector.name?.toLowerCase()) {
      case "metamask":
        return "MetaMask 钱包";
      case "walletconnect":
        return "WalletConnect";
      case "coinbase wallet":
        return "Coinbase 钱包";
      case "injected":
        return "浏览器钱包";
      default:
        return connector.name || "未知钱包";
    }
  }

  /**
   * 检查钱包是否支持特定功能
   */
  supportsFeature(_feature: "signing" | "switching" | "adding"): boolean {
    const connector = this.currentConnector;
    if (!connector) return false;

    // 这里可以根据不同的连接器类型返回支持的功能
    // 简化版本：大部分钱包都支持基本功能
    return true;
  }

  /**
   * 清理资源
   */
  dispose() {
    if (this.unwatchAccount) {
      this.unwatchAccount();
      this.unwatchAccount = undefined;
    }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): object {
    return {
      walletInfo: this.walletInfo,
      walletStatus: this.getWalletStatus(),
      connectionSummary: this.getConnectionSummary(),
      walletType: this.getWalletTypeDescription(),
    };
  }
}
