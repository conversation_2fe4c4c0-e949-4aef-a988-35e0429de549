import { makeAutoObservable, reaction } from "mobx";
import axios from "axios";
import Token from "@/types/token";
import { wagmiAdapter } from "@/provider";
import { getBalance, readContracts } from "@wagmi/core";
import { ERC20ABI } from "@/config/abi/Erc20Abi";
import { rootStore } from "@/store/index.ts";
import { isValidEvmAddress, isValidContractAddress } from "@/lib/utils";

export class TokenStore {
  private readonly CACHE_DURATION = 3600000;
  private readonly CACHE_KEY = "token_list_cache";
  private readonly CACHE_TIMESTAMP_KEY = "token_list_cache_timestamp";

  tokenList: Token[] = [];

  constructor() {
    makeAutoObservable(this);
    this.watch();
  }

  private watch() {
    reaction(
      () => ({
        tokens: this.tokenList,
        address: rootStore.accountStore.walletAddress,
        isConnected: rootStore.accountStore.isWalletConnected,
      }),
      ({ tokens, address, isConnected }) => {
        console.log("Wallet or tokens changed:", {
          tokensCount: tokens.length,
          address,
          isConnected,
        });

        if (
          isConnected &&
          tokens.length > 0 &&
          address &&
          isValidEvmAddress(address)
        ) {
          console.log("Fetching token balances for connected wallet:", address);
          this.batchFetchTokenBalance(tokens, address);

          // 同时批量查询授权数量
          console.log(
            "Fetching token allowances for connected wallet:",
            address,
          );
          this.batchFetchTokenAllowances(tokens, address);
        } else if (!isConnected) {
          console.log(
            "Wallet disconnected, clearing token balances and allowances",
          );
          this.clearTokenBalances();
          this.clearTokenAllowances();
        }
      },
      {
        fireImmediately: true,
      },
    );
  }

  /**
   * 清空所有代币余额
   */
  private clearTokenBalances() {
    this.tokenList.forEach((token) => {
      token.balance.setValue(0n);
      token.balance.setLoading(false);
    });
  }

  /**
   * 清空所有代币授权数量
   */
  private clearTokenAllowances() {
    this.tokenList.forEach((token) => {
      token.allowanceForCashier.setValue(0n);
      token.allowanceForCashier.setLoading(false);
    });
  }

  private convertToTokenInstances(tokenData: any[]): Token[] {
    return tokenData.map((data) => {
      const token = new Token({});
      Object.assign(token, data);
      if (token.decimals !== undefined) {
        token.balance.setDecimal(token.decimals);
      }
      return token;
    });
  }

  async getTokenList(chainId: number, destChainId: number) {
    const now = Date.now();
    const cacheKey = `${this.CACHE_KEY}_${chainId}_${destChainId}`;
    const timestampKey = `${this.CACHE_TIMESTAMP_KEY}_${chainId}_${destChainId}`;

    const cachedData = localStorage.getItem(cacheKey);
    const cachedTimestamp = localStorage.getItem(timestampKey);

    if (cachedData && cachedTimestamp) {
      const timestamp = parseInt(cachedTimestamp, 10);
      if (now - timestamp < this.CACHE_DURATION) {
        try {
          const parsedData = JSON.parse(cachedData);
          this.tokenList = this.convertToTokenInstances(parsedData);
          return; // 使用缓存数据，直接返回
        } catch (error) {
          console.error("Failed to parse cached token list:", error);
          localStorage.removeItem(cacheKey);
          localStorage.removeItem(timestampKey);
        }
      }
    }

    try {
      const response = await axios.get(
        `http://localhost:9527/tube/getTokenList`,
        {
          params: {
            chainId,
            destChainId,
          },
        },
      );

      const tokensData = response.data || [];

      if (tokensData.length > 0) {
        // 缓存原始数据（普通对象）
        localStorage.setItem(cacheKey, JSON.stringify(tokensData));
        localStorage.setItem(timestampKey, now.toString());

        // 转换为 Token 实例并设置到 tokenList
        this.tokenList = this.convertToTokenInstances(tokensData);
      } else {
        this.tokenList = [];
      }
    } catch (error) {
      console.error("Failed to fetch token list:", error);

      // 尝试使用缓存数据作为后备
      if (cachedData) {
        try {
          const parsedData = JSON.parse(cachedData);
          this.tokenList = this.convertToTokenInstances(parsedData);
        } catch (parseError) {
          console.error(
            "Failed to parse cached token list as fallback:",
            parseError,
          );
          this.tokenList = [];
        }
      } else {
        this.tokenList = [];
      }
    }
  }

  async fetchTokenBalance(token: Token, address: string) {
    try {
      if (token.address === "0x0000000000000000000000000000000000000000") {
        const nativeBalance = await getBalance(wagmiAdapter.wagmiConfig, {
          address: address as `0x${string}`,
          chainId: token.chainid,
        });
        token.balance.setValue(nativeBalance.value);
      } else {
      }

      const results = await readContracts(wagmiAdapter.wagmiConfig, {
        contracts: [
          {
            address: token.address as `0x${string}`,
            abi: ERC20ABI.Functions.balanceOf,
            functionName: "balanceOf" as const,
            args: [address as `0x${string}`],
            chainId: token.chainid || 4689,
          },
        ],
      });

      if (results.length > 0) {
        const result = results[0];
        if (result.status === "success" && result.result) {
          token.setBalance(result.result as bigint);
        } else {
          console.warn(
            `Failed to fetch balance for token ${token.symbol || token.address}:`,
            result.error,
          );
        }

        // 取消加载状态
        token.balance.setLoading(false);
      }
    } catch (error) {
      token.setBalance(0n);
      console.error("Failed to batch fetch token balances:", error);
      throw error;
    }
  }

  async batchFetchTokenBalance(tokens: Token[], address: string) {
    tokens.forEach((token) => {
      token.balance.setLoading(true);
    });

    try {
      const nativeToken = tokens.find(
        (token) =>
          token.address === "0x0000000000000000000000000000000000000000",
      );
      if (nativeToken) {
        const nativeBalance = await getBalance(wagmiAdapter.wagmiConfig, {
          address: address as `0x${string}`,
          chainId: nativeToken.chainid,
        });
        nativeToken.balance.setLoading(false);
        nativeToken.balance.setValue(nativeBalance.value);
      }

      const erc20Tokens = tokens.filter(
        (token) =>
          token.address !== "0x0000000000000000000000000000000000000000",
      );
      const contracts = erc20Tokens.map((token) => {
        return {
          address: token.address as `0x${string}`,
          abi: ERC20ABI.Functions.balanceOf,
          functionName: "balanceOf" as const,
          args: [address as `0x${string}`],
          chainId: token.chainid || 4689,
        };
      });

      const results = await readContracts(wagmiAdapter.wagmiConfig, {
        contracts,
      });

      results.forEach((result, index) => {
        const token = erc20Tokens[index];
        token.balance.setLoading(false);
        if (token) {
          if (result.status === "success" && result.result) {
            token.setBalance(result.result as bigint);
          } else {
            console.warn(
              `Failed to fetch balance for token ${token.symbol || token.address}:`,
              result.error,
            );
          }
        }
      });
    } catch (error) {
      tokens.forEach((token) => {
        token.balance.setLoading(false);
      });

      console.error("Failed to batch fetch token balances:", error);
      throw error;
    }
  }

  /**
   * 批量获取代币授权数量
   * @param tokens - 代币列表
   * @param ownerAddress - 代币持有者地址
   */
  async batchFetchTokenAllowances(tokens: Token[], ownerAddress: string) {
    if (!tokens.length || !ownerAddress || !isValidEvmAddress(ownerAddress)) {
      console.warn("Invalid parameters for batch allowance fetch");
      return;
    }

    // 设置所有代币的授权查询为加载状态
    tokens.forEach((token) => {
      if (!token.isNative) {
        token.allowanceForCashier.setLoading(true);
      }
    });

    try {
      // 过滤出非原生代币
      const erc20Tokens = tokens.filter((token) => !token.isNative);

      if (erc20Tokens.length === 0) {
        console.log("No ERC20 tokens to fetch allowances for");
        return;
      }

      // 为每个代币构建合约调用
      const contracts: any[] = [];
      const tokenSpenderPairs: Array<{ token: Token; spenderAddress: string }> =
        [];

      for (const token of erc20Tokens) {
        try {
          // 使用 Token 的内部方法获取 spender 地址
          const spenderAddress = token.getSpenderAddress();
          if (spenderAddress && isValidContractAddress(spenderAddress)) {
            contracts.push({
              address: token.address as `0x${string}`,
              abi: ERC20ABI.Functions.allowance,
              functionName: "allowance" as const,
              args: [
                ownerAddress as `0x${string}`,
                spenderAddress as `0x${string}`,
              ],
              chainId: token.chainid || 4689,
            });
            tokenSpenderPairs.push({ token, spenderAddress });
          } else {
            console.warn(
              `No valid spender address found for token ${token.symbol || token.address}`,
            );
            token.allowanceForCashier.setLoading(false);
          }
        } catch (error) {
          console.warn(
            `Failed to get spender address for token ${token.symbol || token.address}:`,
            // @ts-ignore
            error.message,
          );
          token.allowanceForCashier.setLoading(false);
        }
      }

      if (contracts.length === 0) {
        console.warn("No valid contracts to query allowances for");
        return;
      }

      console.log(`Fetching allowances for ${contracts.length} tokens`);

      // 批量查询授权数量
      const results = await readContracts(wagmiAdapter.wagmiConfig, {
        contracts,
      });

      // 处理查询结果
      results.forEach((result, index) => {
        const { token } = tokenSpenderPairs[index];
        token.allowanceForCashier.setLoading(false);

        if (result.status === "success" && result.result !== undefined) {
          token.allowanceForCashier.setValue(result.result as bigint);
          console.log(
            `✓ Allowance for ${token.symbol}: ${result?.result?.toString()}`,
          );
        } else {
          console.warn(
            `Failed to fetch allowance for token ${token.symbol || token.address}:`,
            result.error,
          );
          token.allowanceForCashier.setValue(0n);
        }
      });
    } catch (error) {
      // 清除所有加载状态
      tokens.forEach((token) => {
        token.allowanceForCashier.setLoading(false);
      });

      console.error("Failed to batch fetch token allowances:", error);
      throw error;
    }
  }
}
