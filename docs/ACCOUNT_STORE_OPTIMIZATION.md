# AccountStore 钱包状态监听优化

## 问题描述

在原始实现中，`tokenStore` 的 `watch` 方法无法正确观察到 `accountStore` 中钱包地址的变化，导致以下问题：

1. **钱包连接时**：TokenStore 无法自动获取新连接钱包的代币余额
2. **钱包断开时**：TokenStore 无法清空代币余额显示
3. **切换账户时**：TokenStore 无法更新为新账户的代币余额

## 根本原因

原始的 `accountStore.walletAddress` 是一个 getter，每次都调用 `getAccount(wagmiAdapter.wagmiConfig)` 来获取最新状态：

```typescript
// 问题代码
get walletAddress(): string | undefined {
  return getAccount(wagmiAdapter.wagmiConfig).address;
}
```

MobX 的 `reaction` 无法追踪到这种外部状态的变化，因为 `getAccount()` 的返回值变化不会触发 MobX 的响应式更新。

## 解决方案

### 1. 使用 wagmi 的 `watchAccount` API

在 `AccountStore` 中使用 wagmi 提供的 `watchAccount` 方法来监听账户状态变化：

```typescript
import { watchAccount } from "@wagmi/core";

export class AccountStore {
  private _walletInfo: WalletInfo = {
    isConnected: false,
    address: undefined,
    chainId: undefined,
    connector: undefined,
  };

  private unwatchAccount?: () => void;

  constructor() {
    makeAutoObservable(this);
    this.initializeWatcher();
  }

  private initializeWatcher() {
    // 监听账户变化
    this.unwatchAccount = watchAccount(wagmiAdapter.wagmiConfig, {
      onChange: (account) => {
        console.log("Account changed:", account);
        this._walletInfo = {
          isConnected: account.isConnected,
          address: account.address,
          chainId: account.chainId,
          connector: account.connector,
        };
      },
    });
  }
}
```

### 2. 使用内部可观察状态

将钱包信息存储在 MobX 可观察的内部状态中，而不是每次都调用外部 API：

```typescript
// 优化后的代码
get walletInfo(): WalletInfo {
  return this._walletInfo; // 返回内部可观察状态
}

get walletAddress(): string | undefined {
  return this._walletInfo.address; // MobX 可以追踪到这个变化
}
```

### 3. 优化 TokenStore 的监听逻辑

在 `tokenStore` 中添加对钱包断开连接的处理：

```typescript
private watch() {
  reaction(
    () => ({
      tokens: this.tokenList,
      address: rootStore.accountStore.walletAddress,
      isConnected: rootStore.accountStore.isWalletConnected,
    }),
    ({ tokens, address, isConnected }) => {
      if (isConnected && tokens.length > 0 && address && isValidEvmAddress(address)) {
        console.log("Fetching token balances for connected wallet:", address);
        this.batchFetchTokenBalance(tokens, address);
      } else if (!isConnected) {
        console.log("Wallet disconnected, clearing token balances");
        this.clearTokenBalances();
      }
    },
    { fireImmediately: true }
  );
}

private clearTokenBalances() {
  this.tokenList.forEach((token) => {
    token.balance.setValue(0n);
    token.balance.setLoading(false);
  });
}
```

## 优化效果

### 1. 钱包连接检测
- ✅ 钱包连接时自动获取代币余额
- ✅ 切换账户时自动更新代币余额
- ✅ 实时响应钱包状态变化

### 2. 钱包断开处理
- ✅ 钱包断开时自动清空代币余额
- ✅ 避免显示过期的余额信息
- ✅ 提供清晰的状态反馈

### 3. 性能优化
- ✅ 减少不必要的 API 调用
- ✅ 使用 MobX 的响应式机制
- ✅ 正确的资源清理

## 使用示例

### 在组件中使用

```typescript
import { observer } from "mobx-react-lite";
import { useStore } from "@/store";

const WalletComponent = observer(() => {
  const { accountStore, tokenStore } = useStore();

  // 这些状态变化现在可以被正确观察到
  const isConnected = accountStore.isWalletConnected;
  const address = accountStore.walletAddress;
  const tokens = tokenStore.tokenList;

  return (
    <div>
      <p>连接状态: {isConnected ? '已连接' : '未连接'}</p>
      <p>钱包地址: {address || '无'}</p>
      <p>代币数量: {tokens.length}</p>
    </div>
  );
});
```

### 监听状态变化

```typescript
import { reaction } from "mobx";

// 监听钱包连接状态变化
const disposer = reaction(
  () => accountStore.isWalletConnected,
  (isConnected) => {
    console.log("钱包连接状态变化:", isConnected);
    if (isConnected) {
      // 钱包连接时的处理逻辑
    } else {
      // 钱包断开时的处理逻辑
    }
  }
);
```

## 测试验证

可以使用提供的测试组件来验证优化效果：

```typescript
import { WalletStatusDemo } from "@/components/WalletStatusDemo";

// 在应用中使用演示组件
<WalletStatusDemo />
```

## 注意事项

1. **资源清理**: 确保在组件卸载或 store 销毁时调用 `accountStore.dispose()` 来清理 `watchAccount` 监听器
2. **初始化顺序**: 确保 wagmi 配置在 AccountStore 初始化之前完成
3. **错误处理**: 在生产环境中添加适当的错误处理逻辑

这次优化解决了钱包状态监听的核心问题，提供了更可靠和响应式的用户体验。
